<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Playwright Insight Engine (PIE)</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Removed Reveal.js dependencies for single-page scrollable layout -->
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="single-page-presentation">
    <div class="cover-slide">
      <div class="cover-bg"></div>
      <div class="floating-keywords">
        <span class="keyword">scikit-learn</span>
        <span class="keyword">TF-IDF</span>
        <span class="keyword">Naive <PERSON>es</span>
        <span class="keyword">Intent Classification</span>
        <span class="keyword">Local ML</span>
        <span class="keyword">API Optimization</span>
      </div>
      <div class="cover-content">
        <div class="tech-icons">
          <div class="tech-icon-placeholder scikit-icon">
            <span>sklearn</span>
          </div>
          <div class="tech-icon-placeholder robot-icon">
            <span>🤖</span>
          </div>
          <div class="tech-icon-placeholder chart-icon">
            <span>📊</span>
          </div>
        </div>
        <div class="cover-ai-img">
          <span class="brain-emoji">🧠</span>
        </div>
<h1 id="pie-title">
  Playwright Insight Engine (PIE) <br>
  <span class="ml-highlight">with Local Machine Learning Model</span>
</h1>

<!-- Full-screen modal overlay -->
<div id="pie-overlay" class="overlay" role="dialog" aria-modal="true" aria-labelledby="pie-modal-title" hidden>
  <div class="modal" role="document">

  <button id="pie-close" class="close" aria-label="Close">×</button>

<header class="modal-header">
  <div class="modal-hero-icon">🧩</div>
  <div class="modal-hero-copy">
    <h2 id="pie-modal-title">What is PIE?</h2>
    <p class="modal-subtitle">Playwright Insight Engine — fast, private test insights powered by local ML.</p>
  </div>
</header>

<section class="capabilities">
  <div class="cap-item">
    <div class="cap-icon">🎭</div>
    <p>Analyzes Playwright test results using a local machine learning model.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">📱</div>
    <p>Classifies user questions (intents) about test outcomes, features, and analytics.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">📊</div>
    <p>Answers queries like “How many tests passed?” instantly — no external API calls.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">🧠</div>
    <p>Provides fast, private, and cost-effective insights into your test suite.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">💬</div>
    <p>Includes a Gradio chatbot interface for interactive Q&amp;A and live demos.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">🔌</div>
    <p>Pluggable pipeline (TF-IDF → Naive Bayes) that you can extend with new intents.</p>
  </div>
</section>

<!-- <footer class="modal-footer">
  <a class="button button-primary" href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank" rel="noopener">
    Open Gradio Demo
  </a>
</footer> -->



<!-- <p class="cap-cta">
  <a class="button" href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank" rel="noopener">
    Open Gradio Demo
  </a>
</p> -->

  </div>
</div>
        <p class="subtitle">Smarter, Faster, and More Private AI Solutions</p>
        <div class="tech-stack">
          <div class="tech-badge">🧠 Machine Learning</div>
          <div class="tech-badge">📊 Model Training</div>
          <div class="tech-badge">⚡ Real-time Classification</div>
          <div class="tech-badge">🔒 Privacy-First</div>
        </div>
        <div class="cover-meta">
          <span class="author">Your Name</span> &nbsp;|&nbsp; <span class="date">August 2025</span>
        </div>
      </div>
      <div class="ml-pipeline">

<!-- WORKFLOW MODAL -->
<div id="workflow-overlay" class="overlay" role="dialog"
     aria-modal="true" aria-labelledby="wf-title" hidden>
  <div class="modal" role="document">
    <button id="workflow-close" class="close" aria-label="Close">×</button>

    <header class="modal-header">
      <div class="modal-hero-icon">🧪</div>
      <div class="modal-hero-copy">
        <h2 id="wf-title">PIE Workflow</h2>
        <p class="modal-subtitle">From fresh test data → TF-IDF → Naive Bayes → answers in chat</p>
      </div>
    </header>

    <!-- DATASET -->
    <section class="wf-section">
      <h3>Dataset</h3>
      <p>
        A <code>test-results.json</code> produced by Playwright when code is pushed to GitHub.
        We always load the <strong>latest</strong> artifact so the model reflects the newest test run.
      </p>
      <ul class="wf-points">
        <li>Generated by CI on each push (GitHub Actions).</li>
        <li>Stored/uploaded where the bot can fetch it (artifact, repo, or Space storage).</li>
        <li>Used as the single source of truth for counts, statuses, and metadata.</li>
      </ul>
    </section>

    <!-- TOOLS -->
    <section class="wf-section">
      <h3>Languages & Tools</h3>
      <div class="badges">
        <span class="badge">Playwright</span>
        <span class="badge">Python</span>
        <span class="badge">scikit-learn</span>
        <span class="badge">NumPy</span>
        <span class="badge">GitHub Actions</span>
        <span class="badge">Gradio</span>
        <span class="badge">Hugging Face Space</span>
      </div>
    </section>

    <!-- PROCESS -->
    <section class="wf-section">
      <h3>Process</h3>
      <ol class="process-list">
        <li>
          <span class="step-chip">1</span>
          <strong>Load latest</strong> <code>test-results.json</code> from GitHub (artifact/repo).
        </li>
        <li>
          <span class="step-chip">2</span>
          <strong>Text vectorization:</strong> build/transform with <abbr title="Term Frequency–Inverse Document Frequency">TF-IDF</abbr>.
        </li>
        <li>
          <span class="step-chip">3</span>
          <strong>Intent prediction:</strong> Multinomial <em>Naive Bayes</em> classifies the user’s question.
        </li>
        <li>
          <span class="step-chip">4</span>
          <strong>Execute intent logic</strong> (e.g., count passed tests, filter by tag, etc.).
        </li>
        <li>
          <span class="step-chip">5</span>
          <strong>Return result</strong> to the user via the Gradio chatbot UI.
        </li>
      </ol>

      <!-- Optional mini flow -->
      <div class="flow-line">
        <span class="flow-node">📝 Dataset</span>
        <span class="flow-arrow">→</span>
        <span class="flow-node">🔄 TF-IDF</span>
        <span class="flow-arrow">→</span>
        <span class="flow-node">🧮 Naive Bayes</span>
        <span class="flow-arrow">→</span>
        <span class="flow-node">🎯 Intent → Answer</span>
      </div>
    </section>

    <footer class="modal-footer">
      <button class="button" id="workflow-close-footer">Close</button>
    </footer>
  </div>
</div>





        <div class="pipeline-step">
          <div class="step-icon">📝</div>
          <div class="step-label">Data Input</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🔄</div>
          <div class="step-label">TF-IDF</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🧮</div>
          <div class="step-label">Naive Bayes</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🎯</div>
          <div class="step-label">Intent</div>
        </div>

        <!-- Trigger button placed after pipeline -->
        <div class="pipeline-actions">
          <button id="view-workflow-btn" class="button button-primary">View Workflow Details</button>
        </div>
      </div>
    </div>
    <div class="presentation-section">
      <h2>Problem Statement</h2>
      <ul>
        <li>Frequent API calls increase costs and latency</li>
        <li>External dependencies can be slow or unreliable</li>
        <li>Need for a faster, more private solution</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Solution Overview</h2>
      <ul>
        <li>Train a custom ML model for intent classification</li>
        <li>Run the model locally (or on Hugging Face Space)</li>
        <li>Reduce or eliminate unnecessary API calls</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Technical Approach</h2>
      <ul>
        <li>Data: Playwright test results and analytical questions</li>
        <li>Model: Naive Bayes classifier (scikit-learn)</li>
        <li>Frontend: Gradio app for user interaction</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Demo</h2>
      <p>Try the app live:</p>
      <p><a href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank">Open Gradio Demo</a></p>
      <p><em>(Replace with your actual Space link)</em></p>
    </div>
    <div class="presentation-section">
      <h2>Impact</h2>
      <ul>
        <li>Fewer API calls → lower costs</li>
        <li>Faster response times</li>
        <li>Improved privacy and reliability</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>How the Test Intent Classifier Works</h2>
      <ol>
        <li><strong>User Input:</strong> You type a question, e.g., “How many tests passed?”</li>
        <li><strong>Text Vectorization:</strong> The input is converted into a TF-IDF vector (numbers representing word importance).</li>
        <li><strong>Intent Prediction:</strong> The Multinomial Naive Bayes model predicts which intent (label) best matches the input, e.g., <code>test_count_passed</code>.</li>
        <li><strong>Intent Handling:</strong> The chatbot runs logic for that intent (e.g., counts passed tests in <code>test-results.json</code>).</li>
        <li><strong>Response Generation:</strong> The chatbot formats and returns the answer to you in the chat.</li>
      </ol>
      <p style="margin-top:2em; font-style:italic; color:#555;">The model’s “brain” only decides which intent your question matches. The code then runs the logic for that intent and returns the answer.</p>
    </div>
    <div class="presentation-section">
      <h2>Next Steps</h2>
      <ul>
        <li>Expand training data for more intents</li>
        <li>Integrate with more workflows</li>
        <li>Monitor and improve model accuracy</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Contact</h2>
      <p>Your Name</p>
      <p>Email: <EMAIL></p>
      <p>GitHub: <a href="https://github.com/your-username" target="_blank">your-username</a></p>
      <p>Hugging Face: <a href="https://huggingface.co/your-username" target="_blank">your-username</a></p>
    </div>
  </div>
  <!-- Reveal.js scripts removed for single-page layout -->


  <script>
  (() => {
    const title = document.getElementById('pie-title');
    const overlay = document.getElementById('pie-overlay');
    const closeBtn = document.getElementById('pie-close');
    let lastFocused;

    const focusableSelector = [
      'a[href]',
      'area[href]',
      'button:not([disabled])',
      'input:not([disabled]):not([type="hidden"])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(',');

    function openModal() {
      lastFocused = document.activeElement;
      overlay.hidden = false;
      overlay.classList.add('show');
      document.body.classList.add('modal-open');
      // Focus first focusable element in modal (close button fallback)
      const focusables = overlay.querySelectorAll(focusableSelector);
      (focusables[0] || closeBtn).focus();
    }

    function closeModal() {
      overlay.classList.remove('show');
      document.body.classList.remove('modal-open');
      // Wait for transition to complete before hiding
      setTimeout(() => {
        overlay.hidden = true;
      }, 250);
      if (lastFocused) lastFocused.focus();
    }

    // Title opens modal (click)
    title.addEventListener('click', openModal);

    // Close actions
    closeBtn.addEventListener('click', closeModal);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) closeModal(); // backdrop click
    });
    document.addEventListener('keydown', (e) => {
      if (!overlay.hidden && e.key === 'Escape') closeModal();
    });

    // Basic focus trap
    overlay.addEventListener('keydown', (e) => {
      if (overlay.hidden || e.key !== 'Tab') return;
      const nodes = Array.from(overlay.querySelectorAll(focusableSelector));
      if (nodes.length === 0) return;
      const first = nodes[0];
      const last = nodes[nodes.length - 1];
      const active = document.activeElement;

      if (e.shiftKey && active === first) { // Shift+Tab on first -> last
        e.preventDefault();
        last.focus();
      } else if (!e.shiftKey && active === last) { // Tab on last -> first
        e.preventDefault();
        first.focus();
      }
    });
  })();

 // ===== Workflow Modal =====
  const wfOverlay = document.getElementById('workflow-overlay');
  const wfClose   = document.getElementById('workflow-close');
  const wfClose2  = document.getElementById('workflow-close-footer');
  const wfBtn     = document.getElementById('view-workflow-btn');
  const pipeline  = document.querySelector('.ml-pipeline');

  function openWorkflow() {
    wfOverlay.hidden = false;
    wfOverlay.classList.add('show');     // uses the same .overlay.show CSS
    document.body.classList.add('modal-open');
    (wfClose || wfOverlay).focus();
  }
  function closeWorkflow() {
    wfOverlay.classList.remove('show');
    setTimeout(() => {
      wfOverlay.hidden = true;
      document.body.classList.remove('modal-open');
    }, 250);
  }

  // Open by clicking any pipeline step or the button
  if (pipeline) pipeline.addEventListener('click', openWorkflow);
  if (wfBtn)     wfBtn.addEventListener('click', openWorkflow);

  // Close handlers
  wfClose?.addEventListener('click', closeWorkflow);
  wfClose2?.addEventListener('click', closeWorkflow);
  wfOverlay.addEventListener('click', (e) => {
    if (e.target === wfOverlay) closeWorkflow(); // backdrop click
  });
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && !wfOverlay.hidden) closeWorkflow();
  });
  
</script>

</body>
</html>