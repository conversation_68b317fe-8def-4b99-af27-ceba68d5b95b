<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Playwright Insight Engine (PIE)</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Removed Reveal.js dependencies for single-page scrollable layout -->
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="single-page-presentation">
    <div class="cover-slide">
      <div class="cover-bg"></div>
      <div class="floating-keywords">
        <span class="keyword">scikit-learn</span>
        <span class="keyword">TF-IDF</span>
        <span class="keyword">Naive <PERSON>es</span>
        <span class="keyword">Intent Classification</span>
        <span class="keyword">Local ML</span>
        <span class="keyword">API Optimization</span>
      </div>
      <div class="cover-content">
        <div class="tech-icons">
          <div class="tech-icon-placeholder scikit-icon">
            <span>sklearn</span>
          </div>
          <div class="tech-icon-placeholder robot-icon">
            <span>🤖</span>
          </div>
          <div class="tech-icon-placeholder chart-icon">
            <span>📊</span>
          </div>
        </div>
        <div class="cover-ai-img">
          <span class="brain-emoji">🧠</span>
        </div>
<h1 id="pie-title">
  Playwright Insight Engine (PIE) <br>
  <span class="ml-highlight">with Local Machine Learning Model</span>
</h1>

<!-- Full-screen modal overlay -->
<div id="pie-overlay" class="overlay" role="dialog" aria-modal="true" aria-labelledby="pie-modal-title" hidden>
  <div class="modal" role="document">

  <button id="pie-close" class="close" aria-label="Close">×</button>

<header class="modal-header">
  <div class="modal-hero-icon">🧩</div>
  <div class="modal-hero-copy">
    <h2 id="pie-modal-title">What is PIE?</h2>
    <p class="modal-subtitle">Playwright Insight Engine — fast, private test insights powered by local ML.</p>
  </div>
</header>

<section class="capabilities">
  <div class="cap-item">
    <div class="cap-icon">🎭</div>
    <p>Analyzes Playwright test results using a local machine learning model.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">📱</div>
    <p>Classifies user questions (intents) about test outcomes, features, and analytics.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">📊</div>
    <p>Answers queries like “How many tests passed?” instantly — no external API calls.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">🧠</div>
    <p>Provides fast, private, and cost-effective insights into your test suite.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">💬</div>
    <p>Includes a Gradio chatbot interface for interactive Q&amp;A and live demos.</p>
  </div>

  <div class="cap-item">
    <div class="cap-icon">🔌</div>
    <p>Pluggable pipeline (TF-IDF → Naive Bayes) that you can extend with new intents.</p>
  </div>
</section>

<footer class="modal-footer">
  <a class="button button-primary" href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank" rel="noopener">
    Open Gradio Demo
  </a>
</footer>



<!-- <p class="cap-cta">
  <a class="button" href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank" rel="noopener">
    Open Gradio Demo
  </a>
</p> -->

  </div>
</div>
        <p class="subtitle">Smarter, Faster, and More Private AI Solutions</p>
        <div class="tech-stack">
          <div class="tech-badge">🧠 Machine Learning</div>
          <div class="tech-badge">📊 Model Training</div>
          <div class="tech-badge">⚡ Real-time Classification</div>
          <div class="tech-badge">🔒 Privacy-First</div>
        </div>
        <div class="cover-meta">
          <span class="author">Your Name</span> &nbsp;|&nbsp; <span class="date">August 2025</span>
        </div>
      </div>
      <div class="ml-pipeline">
        <div class="pipeline-step">
          <div class="step-icon">📝</div>
          <div class="step-label">Data Input</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🔄</div>
          <div class="step-label">TF-IDF</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🧮</div>
          <div class="step-label">Naive Bayes</div>
        </div>
        <div class="pipeline-arrow">→</div>
        <div class="pipeline-step">
          <div class="step-icon">🎯</div>
          <div class="step-label">Intent</div>
        </div>
      </div>
    </div>
    <div class="presentation-section">
      <h2>Problem Statement</h2>
      <ul>
        <li>Frequent API calls increase costs and latency</li>
        <li>External dependencies can be slow or unreliable</li>
        <li>Need for a faster, more private solution</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Solution Overview</h2>
      <ul>
        <li>Train a custom ML model for intent classification</li>
        <li>Run the model locally (or on Hugging Face Space)</li>
        <li>Reduce or eliminate unnecessary API calls</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Technical Approach</h2>
      <ul>
        <li>Data: Playwright test results and analytical questions</li>
        <li>Model: Naive Bayes classifier (scikit-learn)</li>
        <li>Frontend: Gradio app for user interaction</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Demo</h2>
      <p>Try the app live:</p>
      <p><a href="https://huggingface.co/spaces/your-username/your-space-name" target="_blank">Open Gradio Demo</a></p>
      <p><em>(Replace with your actual Space link)</em></p>
    </div>
    <div class="presentation-section">
      <h2>Impact</h2>
      <ul>
        <li>Fewer API calls → lower costs</li>
        <li>Faster response times</li>
        <li>Improved privacy and reliability</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>How the Test Intent Classifier Works</h2>
      <ol>
        <li><strong>User Input:</strong> You type a question, e.g., “How many tests passed?”</li>
        <li><strong>Text Vectorization:</strong> The input is converted into a TF-IDF vector (numbers representing word importance).</li>
        <li><strong>Intent Prediction:</strong> The Multinomial Naive Bayes model predicts which intent (label) best matches the input, e.g., <code>test_count_passed</code>.</li>
        <li><strong>Intent Handling:</strong> The chatbot runs logic for that intent (e.g., counts passed tests in <code>test-results.json</code>).</li>
        <li><strong>Response Generation:</strong> The chatbot formats and returns the answer to you in the chat.</li>
      </ol>
      <p style="margin-top:2em; font-style:italic; color:#555;">The model’s “brain” only decides which intent your question matches. The code then runs the logic for that intent and returns the answer.</p>
    </div>
    <div class="presentation-section">
      <h2>Next Steps</h2>
      <ul>
        <li>Expand training data for more intents</li>
        <li>Integrate with more workflows</li>
        <li>Monitor and improve model accuracy</li>
      </ul>
    </div>
    <div class="presentation-section">
      <h2>Contact</h2>
      <p>Your Name</p>
      <p>Email: <EMAIL></p>
      <p>GitHub: <a href="https://github.com/your-username" target="_blank">your-username</a></p>
      <p>Hugging Face: <a href="https://huggingface.co/your-username" target="_blank">your-username</a></p>
    </div>
  </div>
  <!-- Reveal.js scripts removed for single-page layout -->


  <script>
  (() => {
    const title = document.getElementById('pie-title');
    const overlay = document.getElementById('pie-overlay');
    const closeBtn = document.getElementById('pie-close');
    let lastFocused;

    const focusableSelector = [
      'a[href]',
      'area[href]',
      'button:not([disabled])',
      'input:not([disabled]):not([type="hidden"])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(',');

    function openModal() {
      lastFocused = document.activeElement;
      overlay.hidden = false;
      document.body.classList.add('modal-open');
      // Focus first focusable element in modal (close button fallback)
      const focusables = overlay.querySelectorAll(focusableSelector);
      (focusables[0] || closeBtn).focus();
    }

    function closeModal() {
      overlay.hidden = true;
      document.body.classList.remove('modal-open');
      if (lastFocused) lastFocused.focus();
    }

    // Title opens modal (click)
    title.addEventListener('click', openModal);

    // Close actions
    closeBtn.addEventListener('click', closeModal);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) closeModal(); // backdrop click
    });
    document.addEventListener('keydown', (e) => {
      if (!overlay.hidden && e.key === 'Escape') closeModal();
    });

    // Basic focus trap
    overlay.addEventListener('keydown', (e) => {
      if (overlay.hidden || e.key !== 'Tab') return;
      const nodes = Array.from(overlay.querySelectorAll(focusableSelector));
      if (nodes.length === 0) return;
      const first = nodes[0];
      const last = nodes[nodes.length - 1];
      const active = document.activeElement;

      if (e.shiftKey && active === first) { // Shift+Tab on first -> last
        e.preventDefault();
        last.focus();
      } else if (!e.shiftKey && active === last) { // Tab on last -> first
        e.preventDefault();
        first.focus();
      }
    });
  })();
</script>

</body>
</html>