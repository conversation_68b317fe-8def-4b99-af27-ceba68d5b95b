/* --- Base Styles --- */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');
/* Make the hidden attribute actually hide elements, even if .overlay sets display:flex */
[hidden] { display: none !important; }
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Arial, sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: #F3F3F3;
  line-height: 1.6;
  overflow-x: hidden;
}

/* --- Cover Slide Styles --- */
.cover-slide {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
}

.cover-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  z-index: -1;
}

.cover-content {
  z-index: 1;
  max-width: 900px;
  padding: 0 2rem;
  position: relative;
}

/* Floating Keywords Animation */
.floating-keywords {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.keyword {
  position: absolute;
  background: rgba(0, 230, 216, 0.1);
  border: 1px solid rgba(0, 230, 216, 0.3);
  color: #00E6D8;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;
}

.keyword:nth-child(1) { top: 15%; left: 10%; animation-delay: 0s; }
.keyword:nth-child(2) { top: 25%; right: 15%; animation-delay: 1s; }
.keyword:nth-child(3) { top: 45%; left: 5%; animation-delay: 2s; }
.keyword:nth-child(4) { top: 35%; right: 8%; animation-delay: 3s; }
.keyword:nth-child(5) { top: 65%; left: 12%; animation-delay: 4s; }
.keyword:nth-child(6) { top: 70%; right: 20%; animation-delay: 5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  50% { transform: translateY(-20px) rotate(2deg); opacity: 1; }
}

/* Tech Icons */
.tech-icons {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.tech-icon-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 8px 20px rgba(0,0,0,0.3);
  animation: bounce 2s ease-in-out infinite;
  border: 2px solid rgba(255,255,255,0.2);
}

.scikit-icon {
  background: linear-gradient(135deg, #F7931E, #FF6B35);
  color: white;
  font-size: 0.9rem;
  font-family: 'Montserrat', sans-serif;
}

.robot-icon {
  background: linear-gradient(135deg, #00E6D8, #0097A7);
  font-size: 2rem;
}

.chart-icon {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  font-size: 2rem;
}

.tech-icon-placeholder:nth-child(1) { animation-delay: 0s; }
.tech-icon-placeholder:nth-child(2) { animation-delay: 0.5s; }
.tech-icon-placeholder:nth-child(3) { animation-delay: 1s; }

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.cover-ai-img {
  width: 140px;
  height: 140px;
  margin-bottom: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12px 30px rgba(0,0,0,0.4);
  border: 3px solid rgba(255,255,255,0.2);
  animation: brainPulse 3s ease-in-out infinite;
  margin-left: auto;
  margin-right: auto;
}

.brain-emoji {
  font-size: 4rem;
  animation: brainFloat 2s ease-in-out infinite;
}

@keyframes brainPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 12px 30px rgba(0,0,0,0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 16px 40px rgba(102, 126, 234, 0.3);
  }
}

@keyframes brainFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.cover-slide h1 {
  font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 1rem;
  text-shadow: 0 4px 16px rgba(0,0,0,0.3);
  line-height: 1.2;
}

.ml-highlight {
  color: #00E6D8;
  background: linear-gradient(45deg, #00E6D8, #FF9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.5rem;
  color: #B0BEC5;
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Tech Stack Badges */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

.tech-badge {
  background: linear-gradient(45deg, rgba(0, 230, 216, 0.2), rgba(255, 152, 0, 0.2));
  border: 1px solid rgba(0, 230, 216, 0.4);
  color: #FFFFFF;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: default;
}

.tech-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 230, 216, 0.3);
}

.cover-meta {
  font-size: 1.1rem;
  color: #78909C;
  font-weight: 400;
  margin-top: 1rem;
}

.author, .date {
  color: #90A4AE;
}

/* ML Pipeline Visualization */
.ml-pipeline {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pipeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.step-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #00E6D8, #FF9800);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  animation: pulse 2s ease-in-out infinite;
}

.step-label {
  font-size: 0.85rem;
  color: #B0BEC5;
  font-weight: 500;
  text-align: center;
}

.pipeline-arrow {
  font-size: 1.5rem;
  color: #00E6D8;
  animation: slide 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes slide {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5px); }
}

@media (max-width: 768px) {
  .cover-slide h1 {
    font-size: 2.5rem;
  }
  .subtitle {
    font-size: 1.2rem;
  }
  .cover-ai-img {
    width: 80px;
    height: 80px;
  }
  .tech-icons {
    gap: 1rem;
  }
  .tech-icon-placeholder {
    width: 60px;
    height: 60px;
  }
  .scikit-icon {
    font-size: 0.7rem;
  }
  .robot-icon, .chart-icon {
    font-size: 1.5rem;
  }
  .tech-stack {
    gap: 0.5rem;
  }
  .tech-badge {
    font-size: 0.8rem;
    padding: 8px 16px;
  }
  .ml-pipeline {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  .pipeline-arrow {
    transform: rotate(90deg);
  }
  .keyword {
    font-size: 0.7rem;
    padding: 6px 12px;
  }
  .floating-keywords {
    display: none; /* Hide floating keywords on mobile for cleaner look */
  }
}

/* --- Single Page Presentation Section Styles --- */

.single-page-presentation {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
  }
  
  
  .presentation-section {
    background: rgba(255,255,255,0.10);
    border-radius: 0;
    box-shadow: 0 4px 32px rgba(0,0,0,0.10);
    margin: 0 0 36px 0;
    padding: 36px 8vw 28px 8vw;
    transition: background 0.3s;
    min-height: 220px;
    width: 100vw;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  .presentation-section h2 {
    color: #00E6D8;
    font-family: 'Montserrat', 'Segoe UI', Arial, sans-serif;
    letter-spacing: 1px;
    font-size: 2em;
    margin-bottom: 0.7em;
    text-shadow: 0 2px 8px rgba(0,0,0,0.18);
  }
  
  .presentation-section ul,
  .presentation-section ol {
    font-size: 1.18em;
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    padding-left: 1.5em;
    color: #F3F3F3;
  }
  
  .presentation-section ul li,
  .presentation-section ol li {
    margin-bottom: 0.7em;
    line-height: 1.5;
    position: relative;
    padding-left: 0.5em;
  }
  .presentation-section ul li::before {
    content: '•';
    color: #FF9800;
    font-size: 1.2em;
    position: absolute;
    left: -1em;
    top: 0.1em;
  }
  
  .presentation-section a {
    color: #FF9800;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s;
  }
  .presentation-section a:hover {
    color: #FFD740;
  }
  
  .presentation-section p,
  .presentation-section li {
    font-family: 'Segoe UI', Arial, sans-serif;
    color: #F3F3F3;
  }
  
  @media (max-width: 700px) {
    .presentation-section {
      padding: 18px 2vw;
      min-height: 120px;
    }
    .presentation-section h2 {
      font-size: 1.3em;
    }
  }

 /* Make overlay feel richer */
.overlay {
  position: fixed; inset: 0;
  display: flex; align-items: center; justify-content: center;
  background: rgba(20, 16, 31, 0.55);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  z-index: 9999;
  opacity: 0; transition: opacity .25s ease;
}
.overlay.show { opacity: 1; }

/* Bigger, high-contrast dialog */
.modal {
  background: #fff;
  color: #1a1a1a;
  width: min(1200px, 96vw);                /* Much wider modal */
  max-height: 90vh;                        /* Limit height for scrolling */
  border-radius: 18px;
  box-shadow: 0 24px 80px rgba(0,0,0,.35);
  position: relative;
  overflow-y: auto;                        /* Allow scrolling if needed */
  /* Larger typography */
  font-size: clamp(18px, 1.3vw + .8rem, 22px);
  line-height: 1.6;
  transform: translateY(8px) scale(.985);
  opacity: 0;
  transition: transform .25s ease, opacity .25s ease;
}
.overlay.show .modal {
  transform: translateY(0) scale(1);
  opacity: 1;
}

/* Header with gradient band */
.modal-header {
  display: grid;
  grid-template-columns: 80px 1fr;
  gap: 1.5rem;
  padding: 2rem 2.5rem;
  background: linear-gradient(135deg, #6f5aa8 0%, #8a77c2 45%, #b19adf 100%);
  color: #fff;
}
.modal-hero-icon {
  width: 80px; height: 80px;
  display: grid; place-items: center;
  background: rgba(255,255,255,0.18);
  border: 1px solid rgba(255,255,255,.35);
  border-radius: 20px;
  font-size: 2.2rem;
}
.modal-hero-copy h2 {
  margin: 0 0 .5rem 0;
  font-weight: 700;
  letter-spacing: .2px;
  font-size: 2.2rem;
}
.modal-subtitle {
  margin: 0;
  opacity: .95;
  color: #f6f3ff;
  font-size: 1.3rem;
}

/* Body */
.capabilities {
  padding: 2rem 2.5rem 0 2.5rem;
  display: grid;
  grid-template-columns: 1fr 1fr;          /* two columns */
  gap: 1.8rem 2rem;
}
@media (max-width: 720px) {
  .capabilities {
    grid-template-columns: 1fr;
    padding: 1.5rem 1.5rem 0 1.5rem;
  }
}

/* Items */
.cap-item {
  display: grid;
  grid-template-columns: 70px 1fr;
  gap: 1.2rem;
  align-items: start;
  background: #faf8ff;
  border: 1px solid #eee9ff;
  border-radius: 16px;
  padding: 1.3rem;
}
.cap-item p {
  margin: 0;
  color: #2a2a2a;
  font-size: 1.1rem;
  line-height: 1.5;
}

/* Icon chips */
.cap-icon {
  width: 70px; height: 70px;
  display: grid; place-items: center;
  border-radius: 50%;
  background: #8a77c2;
  color: #fff;
  font-size: 1.8rem;
  box-shadow: inset 0 0 0 2px rgba(255,255,255,.35),
              0 6px 16px rgba(138,119,194,.35);
  user-select: none;
}

/* Footer */
.modal-footer {
  padding: 1.8rem 2.5rem 2.5rem 2.5rem;
  display: flex; justify-content: flex-end; gap: 1rem;
}

/* Buttons */
.button {
  display: inline-block;
  padding: 1rem 1.8rem;
  border-radius: 14px;
  text-decoration: none;
  border: 1px solid #ded8f4;
  font-weight: 600;
  font-size: 1.1rem;
}
.button-primary {
  background: #6f5aa8;
  color: #fff;
  border-color: #6f5aa8;
}
.button-primary:hover { filter: brightness(1.05); }

/* Close button */
.close {
  position: absolute; top: 15px; right: 20px;
  border: 0; background: transparent; cursor: pointer;
  font-size: 2.5rem; line-height: 1; color: #ffffffcc;
  z-index: 2;
  padding: 5px;
}
.close:hover { color: #fff; }

/* Ensure text stays readable */
.modal h2 { color: #fff; }                 /* header title on gradient */
.modal p, .modal li { color: #2a2a2a; }

/* Respect hidden attribute */
[hidden] { display: none !important; }

/* Motion-safe */
@media (prefers-reduced-motion: reduce) {
  .overlay, .modal { transition: none; }
  .modal { transform: none; opacity: 1; }
}

/* Trigger under pipeline */
.pipeline-actions {
  margin-top: 1.5rem;
  text-align: center;
  padding: 1rem 0;
}

/* Section spacing - match the larger modal design */
.wf-section {
  padding: 2rem 2.5rem 1rem 2.5rem;
  border-bottom: 1px solid #f0f0f0;
}
.wf-section:last-of-type {
  border-bottom: none;
}
.wf-section h3 {
  margin: 0 0 1rem 0;
  color: #1a1a1a;
  font-size: 1.4rem;
  font-weight: 700;
}
.wf-section p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #2a2a2a;
  margin-bottom: 1rem;
}

/* Bullet points */
.wf-points {
  margin: 1rem 0 0 0;
  padding-left: 1.5rem;
  color: #2a2a2a;
}
.wf-points li {
  margin: .6rem 0;
  font-size: 1.05rem;
  line-height: 1.5;
}

/* Badges for tools */
.badges {
  display: flex;
  flex-wrap: wrap;
  gap: .8rem;
  margin-top: 1rem;
}
.badge {
  padding: .6rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1rem;
  border: 1px solid #ddd;
  background: #faf8ff;
  color: #6f5aa8;
}

/* Process list with numbered chips */
.process-list {
  counter-reset: step;
  list-style: none;
  padding: 0;
  margin: 1rem 0 2rem 0;
}
.process-list li {
  display: grid;
  grid-template-columns: 50px 1fr;
  align-items: start;
  gap: 1.2rem;
  margin: 1rem 0;
  color: #2a2a2a;
  font-size: 1.05rem;
  line-height: 1.5;
}
.step-chip {
  width: 50px;
  height: 50px;
  display: grid;
  place-items: center;
  border-radius: 50%;
  background: #8a77c2;
  color: #fff;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 8px 20px rgba(138,119,194,.3);
}

/* Inline flow line */
.flow-line {
  display: flex;
  align-items: center;
  gap: .8rem;
  margin-top: 2rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 1.5rem;
  background: #f8f6ff;
  border-radius: 16px;
  border: 1px solid #e7e0ff;
}
.flow-node {
  background: #fff;
  border: 2px solid #8a77c2;
  border-radius: 16px;
  padding: .8rem 1.2rem;
  font-weight: 600;
  font-size: 1rem;
  color: #6f5aa8;
  box-shadow: 0 4px 12px rgba(138,119,194,.15);
}
.flow-arrow {
  opacity: .8;
  font-size: 1.5rem;
  color: #8a77c2;
}

/* Mobile responsiveness for workflow modal */
@media (max-width: 768px) {
  .wf-section {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .wf-section h3 {
    font-size: 1.2rem;
  }

  .wf-section p {
    font-size: 1rem;
  }

  .process-list li {
    grid-template-columns: 40px 1fr;
    gap: 1rem;
    font-size: 1rem;
  }

  .step-chip {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .flow-line {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .badges {
    gap: .5rem;
  }

  .badge {
    padding: .5rem .8rem;
    font-size: .9rem;
  }
}

