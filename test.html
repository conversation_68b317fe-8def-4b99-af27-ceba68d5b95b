
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Real‑Data Workflow — test_results.json → TF‑IDF → Intent Match → Naive Bayes → Bot</title>
  <style>
    :root{
      --bg:#0b1220; --text:#e6ecff; --muted:#9fb2ff; --panel:#0f1832; --panel2:#131f3b;
      --cyan:#22d3ee; --amber:#f59e0b; --pink:#f472b6; --green:#4ade80; --glow:#3fa9ff;
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{margin:0; font-family:Inter,ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;
      background: radial-gradient(1000px 600px at 65% -10%, #1a2240 0%, #0b1220 60%), var(--bg);
      color:var(--text); display:flex; align-items:center; justify-content:center; padding:24px}
    .wrap{width:min(1200px,95vw)}
    .title{ text-align:center; font-weight:800; font-size:clamp(20px,2.6vw,32px); margin-bottom:6px }
    .subtitle{ text-align:center; color:var(--muted); margin-bottom:18px; font-size:14px }

    /* Layout: controls + pipeline */
    .top{display:grid; grid-template-columns:1.2fr .8fr; gap:14px; margin-bottom:16px}
    .card{background:linear-gradient(180deg,rgba(255,255,255,.06),rgba(255,255,255,.02));
      border:1px solid rgba(255,255,255,.08); border-radius:14px; padding:12px; box-shadow:0 8px 20px rgba(0,0,0,.35)}
    .card h3{margin:0 0 8px; font-size:14px; letter-spacing:.2px; color:var(--muted)}
    textarea, input{width:100%; background:var(--panel2); color:var(--text); border:1px solid rgba(255,255,255,.12);
      border-radius:10px; padding:10px; font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size:12px}
    textarea{height:120px; resize:vertical}
    .row{display:flex; gap:8px; align-items:center}
    .btn{background:#121d38; color:var(--text); border:1px solid rgba(255,255,255,.15); padding:9px 12px; border-radius:10px; cursor:pointer; font-weight:700}
    .btn:hover{filter:brightness(1.1)}

    /* Pipeline grid */
    .pipeline{position:relative; display:grid; grid-template-columns:1fr auto 1fr auto 1fr auto 1fr auto 1.4fr; align-items:stretch; gap:14px;}
    .step{position:relative; border-radius:16px; padding:14px; background:linear-gradient(180deg,rgba(255,255,255,.06),rgba(255,255,255,.02)); border:1px solid rgba(255,255,255,.08); box-shadow:0 6px 16px rgba(0,0,0,.35); min-height:130px}
    .step .head{display:flex; align-items:center; gap:8px; font-weight:800}
    .step .emoji{font-size:22px}
    .step .meta{color:var(--muted); font-size:12px; margin-top:2px}
    .arrow{align-self:center; width:64px; height:6px; border-radius:999px; background:rgba(255,255,255,.12); overflow:hidden; border:1px solid rgba(255,255,255,.08)}
    .arrow::before{content:""; position:absolute; width:100%; height:100%; background:repeating-linear-gradient(90deg, rgba(255,255,255,.45) 0 10px, transparent 10px 18px); animation:slide 1.2s linear infinite}
    .arrow::after{content:""; position:absolute; right:-8px; top:50%; transform:translateY(-50%); border-left:10px solid rgba(255,255,255,.6); border-top:6px solid transparent; border-bottom:6px solid transparent}
    @keyframes slide{from{transform:translateX(-18px)} to{transform:translateX(0)}}

    /* Specific visuals inside steps */
    .jsonview{height:86px; overflow:auto; background:var(--panel2); border:1px solid rgba(255,255,255,.08); border-radius:10px; padding:8px; font-size:11px}
    .bars{display:flex; gap:6px; align-items:flex-end; height:76px; margin-top:8px}
    .bar{width:18px; background:linear-gradient(180deg, rgba(34,211,238,.3), rgba(34,211,238,.7)); border-radius:6px 6px 4px 4px; transform:scaleY(.2); transform-origin:bottom; transition:transform .8s cubic-bezier(.2,.7,.2,1)}
    .bar.yellow{background:linear-gradient(180deg, rgba(245,158,11,.3), rgba(245,158,11,.8))}
    .chips{display:flex; flex-wrap:wrap; gap:6px; margin-top:8px}
    .chip{font-size:11px; padding:6px 8px; border-radius:999px; border:1px solid rgba(255,255,255,.14); background:#111a33}
    .chip.hit{outline:2px solid color-mix(in oklab, var(--green) 60%, transparent); background:#0f2a1b}
    .score{font-size:12px; color:var(--muted); margin-top:6px}
    .bot{display:flex; align-items:flex-start; gap:10px}
    .bubble{background:#0e1c38; border:1px solid rgba(255,255,255,.12); border-radius:14px; padding:10px 12px; font-size:13px; box-shadow:0 6px 16px rgba(0,0,0,.35)}
    .hidden{display:none}

    /* Active/Glow states */
    .step.active{box-shadow:0 12px 28px rgba(0,0,0,.5), 0 0 0 4px rgba(63,169,255,.18)}
    .step.input.active{outline:2px solid color-mix(in oklab, var(--cyan) 50%, transparent)}
    .step.tfidf.active{outline:2px solid color-mix(in oklab, var(--amber) 50%, transparent)}
    .step.intentmatch.active{outline:2px solid color-mix(in oklab, var(--green) 50%, transparent)}
    .step.nb.active{outline:2px solid color-mix(in oklab, var(--pink) 50%, transparent)}

    .note{color:var(--muted); font-size:12px; text-align:center; margin-top:8px}
    @media(max-width:900px){ .top{grid-template-columns:1fr} .pipeline{grid-template-columns:1fr} .arrow{display:none} }
  </style>
</head>
<body>
  <div class="wrap">
    <div class="title">Real‑Data Flow: test_results.json → TF‑IDF (data & query) → Intent Match → Naive Bayes → Bot</div>
    <div class="subtitle">Paste a sample <b>test_results.json</b> and a user question. Click <b>Run</b> to see the pipeline animate with real parsing, vector bars, intent hit, and bot reply.</div>

    <div class="top">
      <div class="card">
        <h3>📝 test_results.json</h3>
        <textarea id="jsonIn" spellcheck="false">{
  "runs": 1,
  "summary": {"passed": 42, "failed": 3, "skipped": 1, "duration_ms": 128734},
  "tests": [
    {"id":"t1","title":"checkout flow - happy path","status":"passed","duration_ms": 1543},
    {"id":"t2","title":"login shows error on bad creds","status":"failed","error":"AssertionError: expected 401","duration_ms": 932},
    {"id":"t3","title":"search returns results","status":"passed","duration_ms": 621}
  ]
}</textarea>
        <div class="row" style="margin-top:8px">
          <button class="btn" id="pretty">Pretty‑print</button>
          <button class="btn" id="minify">Minify</button>
        </div>
      </div>
      <div class="card">
        <h3>💬 User question</h3>
        <input id="q" value="how many tests passed?" />
        <div class="row" style="margin-top:8px">
          <button class="btn" id="run">▶ Run</button>
          <button class="btn" id="reset">↻ Reset</button>
        </div>
        <div class="note">Tip: record this section as a GIF for your slides after clicking Run.</div>
      </div>
    </div>

    <div class="pipeline" id="pipe">
      <!-- 1: Data Input -->
      <div class="step input" id="s1">
        <div class="head"><span class="emoji">📝</span><div>Data Input</div></div>
        <div class="meta">Load latest test_results.json</div>
        <div class="jsonview" id="jsonView"></div>
      </div>
      <div class="arrow"></div>

      <!-- 2: TF‑IDF (data + query) -->
      <div class="step tfidf" id="s2">
        <div class="head"><span class="emoji">🔄</span><div>TF‑IDF Vectorization</div></div>
        <div class="meta">Vectorize dataset & user query</div>
        <div class="bars">
          <div class="bar" id="b1"></div>
          <div class="bar yellow" id="b2"></div>
          <div class="bar" id="b3"></div>
          <div class="bar yellow" id="b4"></div>
          <div class="bar" id="b5"></div>
          <div class="bar yellow" id="b6"></div>
        </div>
        <div class="score" id="simScore">query↔data cosine: —</div>
      </div>
      <div class="arrow"></div>

      <!-- 3: Intent match (semantic label) -->
      <div class="step intentmatch" id="s3">
        <div class="head"><span class="emoji">🎯</span><div>Intent Match</div></div>
        <div class="meta">Select best intent label</div>
        <div class="chips" id="chips"></div>
        <div class="score" id="intentScore">top intent: —</div>
      </div>
      <div class="arrow"></div>

      <!-- 4: Naive Bayes retrieval & Bot output -->
      <div class="step nb" id="s4">
        <div class="head"><span class="emoji">🧮</span><div>Naive Bayes → Bot</div></div>
        <div class="meta">Retrieve response & answer</div>
        <div class="bot" style="margin-top:8px">
          <div class="emoji">🤖</div>
          <div class="bubble" id="botOut">(awaiting input)</div>
        </div>
      </div>
      <div class="arrow"></div>

      <!-- 5: Final (for stats/insights) -->
      <div class="step" id="s5">
        <div class="head"><span class="emoji">📊</span><div>Insight</div></div>
        <div class="meta">Derived from JSON + intent</div>
        <div class="jsonview" id="insight"></div>
      </div>
    </div>

    <div class="note">Pipeline shows: JSON parsed → TF‑IDF bars animate (data/query) → intent chips highlight match → Naive Bayes returns template → Bot outputs computed answer.</div>
  </div>

  <script>
    const jsonIn = document.getElementById('jsonIn');
    const jsonView = document.getElementById('jsonView');
    const qEl = document.getElementById('q');

    const s1 = document.getElementById('s1');
    const s2 = document.getElementById('s2');
    const s3 = document.getElementById('s3');
    const s4 = document.getElementById('s4');
    const s5 = document.getElementById('s5');

    const b = [1,2,3,4,5,6].map(i=> document.getElementById('b'+i));
    const simScore = document.getElementById('simScore');
    const chipsBox = document.getElementById('chips');
    const intentScore = document.getElementById('intentScore');
    const botOut = document.getElementById('botOut');
    const insight = document.getElementById('insight');

    const intents = [
      { id:'pass_count',   label:'Pass Count',   ex:['how many passed','passed tests','tests passed','count pass'], tpl:(data)=>`There are <b>${data.summary.passed}</b> tests passed.` },
      { id:'fail_count',   label:'Fail Count',   ex:['how many failed','failed tests','count fail','what failed'], tpl:(data)=>`You have <b>${data.summary.failed}</b> failing tests.` },
      { id:'duration',     label:'Total Duration', ex:['total time','how long','duration'], tpl:(data)=>`Total run time: <b>${Math.round(data.summary.duration_ms/1000)}s</b>.` },
      { id:'flaky',        label:'Flaky/Intermittent', ex:['flaky','intermittent','sometimes fails'], tpl:(data)=>`Looking for flakiness across retries (demo: none flagged here).` }
    ];

    // Utilities
    const sleep = (ms)=> new Promise(r=> setTimeout(r, ms));
    const pretty = (o)=> JSON.stringify(o, null, 2);

    function cosineSimilarity(a,b){
      const dot = a.reduce((s,v,i)=> s + v*(b[i]||0), 0);
      const na = Math.sqrt(a.reduce((s,v)=> s+v*v,0));
      const nb = Math.sqrt(b.reduce((s,v)=> s+v*v,0));
      return na && nb ? dot/(na*nb) : 0;
    }

    function tfidfVector(tokens, vocab){
      // tiny fake tf-idf: term frequency / sqrt(len) with idf weights
      const idf = {}; vocab.forEach(t=> idf[t] = Math.log(10/(1+ (t.length%5))) + 1);
      const counts = {}; tokens.forEach(t=> counts[t]=(counts[t]||0)+1);
      return vocab.map(t => (counts[t]||0)/Math.sqrt(tokens.length) * idf[t]);
    }

    function tokenize(s){ return (s.toLowerCase().match(/[a-z0-9]+/g)||[]) }

    function buildVocab(data){
      const text = [
        ...data.tests.map(t=> `${t.title} ${t.status} ${t.error||''}`),
        'passed fail error flaky duration time count tests'
      ].join(' ');
      const uniq = Array.from(new Set(tokenize(text)));
      return uniq.slice(0, 24); // small vocab for visuals
    }

    function rand(min,max){ return Math.random()*(max-min)+min }

    function animateBars(vecA, vecB){
      // Mix two vectors (data & query) into 6 bars for visual effect
      const pick = [0,2,4,6,8,10].map(i=> [vecA[i]||0, vecB[i+1]||0]);
      pick.forEach((pair,i)=>{
        const h = Math.min(1, (pair[0]+pair[1]) / (Math.max(...pick.flat())||1) );
        b[i].style.transform = `scaleY(${0.2 + 0.78*h})`;
      });
    }

    function guessIntent(query){
      const q = query.toLowerCase();
      // Simple NB-like scoring by token overlap with examples
      const scores = intents.map(it=>{
        const score = it.ex.reduce((s,ex)=> s + (q.includes(ex) ? 1 : 0), 0);
        return {id:it.id, label:it.label, score};
      }).sort((a,b)=> b.score - a.score);
      return scores;
    }

    function setActive(step){ [s1,s2,s3,s4,s5].forEach(x=> x.classList.remove('active')); step.classList.add('active') }

    function renderChips(scores){
      chipsBox.innerHTML = '';
      scores.forEach((s,i)=>{
        const el = document.createElement('div'); el.className = 'chip'; el.textContent = s.label + ' · ' + s.score.toFixed(2);
        if(i===0) el.classList.add('hit');
        chipsBox.appendChild(el);
      })
    }

    async function run(){
      // Reset visuals
      botOut.innerHTML = '(processing…)';
      insight.textContent = '';
      [b1,b2,b3,b4,b5,b6].forEach(el=> el?.style && (el.style.transform='scaleY(.2)'));

      let data;
      try{ data = JSON.parse(jsonIn.value) }catch(e){ alert('Invalid JSON'); return }

      // Step 1: show parsed JSON
      setActive(s1);
      jsonView.textContent = pretty({summary:data.summary, tests:data.tests.slice(0,3)});
      await sleep(800);

      // Step 2: TF‑IDF both dataset & query
      setActive(s2);
      const vocab = buildVocab(data);
      const dataTokens = buildVocab(data).map(x=>x); // reuse words as pseudo corpus
      const dataVec = tfidfVector(dataTokens, vocab);
      const queryTokens = tokenize(qEl.value);
      const queryVec = tfidfVector(queryTokens, vocab);
      animateBars(dataVec, queryVec);
      const sim = cosineSimilarity(dataVec, queryVec);
      simScore.textContent = `query↔data cosine: ${(sim).toFixed(2)}`;
      await sleep(900);

      // Step 3: Intent match (chips)
      setActive(s3);
      const scores = guessIntent(qEl.value).map((s,i)=> ({...s, score: s.score || (i? Math.max(0, .6 - i*0.15): .9) }));
      renderChips(scores);
      intentScore.textContent = `top intent: ${scores[0].label} (${scores[0].score.toFixed(2)})`;
      await sleep(900);

      // Step 4: Naive Bayes retrieval (template fill) → Bot
      setActive(s4);
      const top = intents.find(it=> it.id===scores[0].id) || intents[0];
      const msg = top.tpl(data);
      botOut.innerHTML = msg;
      await sleep(700);

      // Step 5: Insight panel (computed)
      setActive(s5);
      const extra = {
        pass_rate: ((data.summary.passed)/(data.summary.passed+data.summary.failed) * 100).toFixed(1) + '%',
        total_tests: data.summary.passed + data.summary.failed + (data.summary.skipped||0),
      };
      insight.textContent = pretty(extra);
    }

    function reset(){
      [s1,s2,s3,s4,s5].forEach(x=> x.classList.remove('active'));
      jsonView.textContent = '';
      simScore.textContent = 'query↔data cosine: —';
      intentScore.textContent = 'top intent: —';
      chipsBox.innerHTML = '';
      botOut.textContent = '(awaiting input)';
      insight.textContent = '';
      b.forEach(el=> el.style.transform='scaleY(.2)');
    }

    // Wire controls
    document.getElementById('run').addEventListener('click', run);
    document.getElementById('reset').addEventListener('click', reset);
    document.getElementById('pretty').addEventListener('click', ()=>{
      try{ const o = JSON.parse(jsonIn.value); jsonIn.value = pretty(o) }catch(e){}
    });
    document.getElementById('minify').addEventListener('click', ()=>{
      try{ const o = JSON.parse(jsonIn.value); jsonIn.value = JSON.stringify(o) }catch(e){}
    });

    // Initial preview
    jsonView.textContent = '(ready)';
  </script>
</body>
</html>
